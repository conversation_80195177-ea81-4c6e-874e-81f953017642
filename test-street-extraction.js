/**
 * Test script to verify street number extraction logic
 * This demonstrates how the regex pattern works with various address formats
 */

function extractStreetNumber(formattedAddress, source, existingStreetNumber) {
  // Only apply extraction for PlacesAPI leads
  if (source !== "PlacesAPI") {
    return existingStreetNumber || "";
  }

  // If we already have a street number, use it
  if (existingStreetNumber && existingStreetNumber.trim() !== "") {
    return existingStreetNumber;
  }

  // If no formatted address, return empty
  if (!formattedAddress || formattedAddress.trim() === "") {
    return "";
  }

  // Extract street number using improved regex pattern
  // This pattern looks for numbers (with optional letters) that come after a street name but before a comma
  // It specifically targets the pattern: "StreetName Number, PostalCode City"
  // Pattern: \s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}
  let streetNumberMatch = formattedAddress.match(/\s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}/);

  // If that doesn't work, try a simpler pattern that looks for numbers after a space but not at the start
  // This handles cases where there might not be a comma after the street number
  if (!streetNumberMatch) {
    // Look for pattern: "word space number" but not "number space number" (to avoid postal codes)
    streetNumberMatch = formattedAddress.match(/[A-Za-z]\s+([0-9]+[A-Za-z]?)(?:\s|,|$)/);
  }

  if (streetNumberMatch && streetNumberMatch[1]) {
    console.log(`✅ Extracted street number "${streetNumberMatch[1]}" from address: ${formattedAddress}`);
    return streetNumberMatch[1];
  }

  // No street number found
  console.log(`❌ No street number found in address: ${formattedAddress}`);
  return "";
}

// Test cases
console.log("=== Testing Street Number Extraction ===\n");

const testCases = [
  {
    address: "Bahnhofstrasse 123, 8001 Zürich, Switzerland",
    source: "PlacesAPI",
    existing: null,
    expected: "123"
  },
  {
    address: "Musterstrasse 45A, 3000 Bern, Switzerland",
    source: "PlacesAPI",
    existing: null,
    expected: "45A"
  },
  {
    address: "Hauptstrasse 7B, 4000 Basel, Switzerland",
    source: "PlacesAPI",
    existing: null,
    expected: "7B"
  },
  {
    address: "Seestrasse, 8000 Zürich, Switzerland", // No number
    source: "PlacesAPI",
    existing: null,
    expected: ""
  },
  {
    address: "Bahnhofstrasse 123, 8001 Zürich, Switzerland",
    source: "ERP", // Not PlacesAPI
    existing: null,
    expected: ""
  },
  {
    address: "Bahnhofstrasse 123, 8001 Zürich, Switzerland",
    source: "PlacesAPI",
    existing: "456", // Already has street number
    expected: "456"
  },
  {
    address: "Route de la Gare 15, 1003 Lausanne, Switzerland",
    source: "PlacesAPI",
    existing: null,
    expected: "15"
  },
  {
    address: "Via Roma 88C, 6900 Lugano, Switzerland",
    source: "PlacesAPI",
    existing: null,
    expected: "88C"
  }
];

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}:`);
  console.log(`  Address: ${testCase.address}`);
  console.log(`  Source: ${testCase.source}`);
  console.log(`  Existing: ${testCase.existing || 'null'}`);
  console.log(`  Expected: "${testCase.expected}"`);

  const result = extractStreetNumber(testCase.address, testCase.source, testCase.existing);
  console.log(`  Result: "${result}"`);

  if (result === testCase.expected) {
    console.log(`  ✅ PASS\n`);
    passed++;
  } else {
    console.log(`  ❌ FAIL - Expected "${testCase.expected}" but got "${result}"\n`);
    failed++;
  }
});

console.log("=== Test Summary ===");
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`📊 Total: ${testCases.length}`);

if (failed === 0) {
  console.log("\n🎉 All tests passed! Street number extraction is working correctly.");
} else {
  console.log(`\n⚠️  ${failed} test(s) failed. Please review the implementation.`);
}
